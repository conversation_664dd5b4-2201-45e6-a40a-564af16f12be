// import { Navigate } from "react-router-dom"
// import useRegister from "@hooks/useRegister"
// import { Heading } from "@components/common"
// import { Input } from "@components/form"
// import { Button, Col, Form, Row, Spinner } from "react-bootstrap"

// const Register = () => {
// const { loading, error, accessToken, register, handleSubmit, formErrors, emailOnBlurHandler, submitForm, emailAvailabilityStatus} = useRegister();

// if(accessToken) {
//   return <Navigate to={"/"}/>
// }
//   return (
//     <>
//     <Heading title="User Register" />
//     <Row>
//       <Col md={{ span: 6, offset: 3}}>
//         <Form onSubmit={handleSubmit(submitForm)}>
//           <Input label="First Name" name="firstName" register={register} error={formErrors.firstName?.message}/>
//           <Input label="Last Name" name="lastName" register={register} error={formErrors.lastName?.message}/>
//           <Input label="Email"
//           name="email"
//           register={register}
//           error={formErrors.email?.message
//           ? formErrors.email?.message
//           : emailAvailabilityStatus === "notAvailable"
//           ? "This email address is already registered"
//           : emailAvailabilityStatus === "failed"
//           ? "Error from the server. Please try again later"
//           : ""
//         }
//           onBlur={emailOnBlurHandler}
//           formText={emailAvailabilityStatus === "checking" ? "We are checking the availability of email address. Please wait a moment" : ""}
//           success={emailAvailabilityStatus === "available" ? "This email address is available for registration" : ""}
//           disabled={emailAvailabilityStatus === "checking" ? true : false}
//           />
//           <Input label="Password" name="password" type="password" register={register} error={formErrors.password?.message}/>
//           <Input label="Confirm Password" name="confirmPassword" type="password" register={register} error={formErrors.confirmPassword?.message}/>
//           <Button variant="info" type="submit" style={{color: "white"}}
//             disabled={(emailAvailabilityStatus === "checking" ? true : false) || loading === "pending" }>
//             {loading === "pending" ? ( <> <Spinner animation="border" size="sm"></Spinner> Loading ...</> ) : "Submit"}
//           </Button>
//           {error && <p style={{color: "red", marginTop: "10px"}}>{error}</p>}
//         </Form>
//       </Col>
//     </Row>
//     </>
//   )
// }

// export default Register

import { useForm, type SubmitHandler } from "react-hook-form";
import { Form, Button, Row, Col } from "react-bootstrap";
import { Heading } from "@components/common";
import { zodResolver } from "@hookform/resolvers/zod";
import { signUpSchema, type signUpType } from "@validations/signUpSchema";
import { Input } from "@components/form";
import type React from "react";
import useCheckEmailAvailability from "@hooks/useCheckEmailAvailability";

const Register = () => {
  const {
    register,
    handleSubmit,
    getFieldState,
    trigger,
    formState: { errors },
  } = useForm<signUpType>({
    resolver: zodResolver(signUpSchema),
    mode: "onBlur",
  });
  const submitForm: SubmitHandler<signUpType> = (data) => {
    console.log(data);
  };

  const {
    emailAvailabilityStatus,
    enteredEmail,
    checkEmailAailability,
    resetCheckEmailAvailability,
  } = useCheckEmailAvailability();
  const emailOnBlurHandler = async (e: React.FocusEvent<HTMLInputElement>) => {
    await trigger("email");
    const { isDirty, invalid } = getFieldState("email");
    const value = e.target.value;
    if (isDirty && !invalid && enteredEmail !== value) {
      checkEmailAailability(value);
    }
    if (isDirty && invalid && enteredEmail) {
      resetCheckEmailAvailability();
    }
  };
  return (
    <>
      <Heading title="User Registration" />
      <Row>
        <Col md={{ span: 6, offset: 3 }}>
          <Form onSubmit={handleSubmit(submitForm)}>
            <Input
              name="firstName"
              label="First Name"
              register={register}
              type="text"
              error={errors.firstName?.message}
              key={"firstName"}
            />
            <Input
              name="lastName"
              label="Last Name"
              register={register}
              type="text"
              error={errors.lastName?.message}
              key={"lastName"}
            />
            <Input
              name="email"
              label="Email"
              register={register}
              type="text"
              error={
                errors.email?.message
                  ? errors.email?.message
                  : emailAvailabilityStatus === "notAvailable"
                  ? "This email address is already registered"
                  : ""
              }
              key={"email"}
              onBlur={emailOnBlurHandler}
              formText={
                emailAvailabilityStatus === "checking"
                  ? "We are checking the availability of email address. Please wait a moment"
                  : ""
              }
              success={
                emailAvailabilityStatus === "available"
                  ? "This email address is available for registration"
                  : ""
              }
            />
            <Input
              name="password"
              label="Password"
              register={register}
              type="password"
              error={errors.password?.message}
              key={"password"}
            />
            <Input
              name="confirmPassword"
              label="Confirm Password"
              register={register}
              type="password"
              error={errors.confirmPassword?.message}
              key={"confirmPassword"}
            />
            <Button
              variant="info"
              type="submit"
              className="mb-3"
              style={{ color: "white" }}
            >
              Submit
            </Button>
          </Form>
        </Col>
      </Row>
    </>
  );
};

export default Register;
