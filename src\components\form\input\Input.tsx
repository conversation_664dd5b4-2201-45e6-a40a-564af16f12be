// import type { FieldValues, Path, UseFormRegister } from "react-hook-form"
// import { Form } from "react-bootstrap"
// import type React from "react"

// type InputProps<TFieldValue extends FieldValues> = {
//     label: string
//     type?: string,
//     name: Path<TFieldValue>,
//     register: UseFormRegister<TFieldValue>,
//     error?: string,
//     onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void,
//     formText?: string,
//     success?: string,
//     disabled?: boolean,
// }

// const Input = <TFieldValue extends FieldValues>({label, type="text", name, register, error, onBlur, formText, success, disabled}: InputProps<TFieldValue>) => {
//   const onBlurHandler = (e: React.FocusEvent<HTMLInputElement>) =>{
//     if(onBlur){
//       onBlur(e);
//       register(name).onBlur(e);
//     }else{
//       register(name).onBlur(e);
//     }
//   } 
//   return (
//     <Form.Group className="mb-3">
//         <Form.Label>{label}</Form.Label>
//         <Form.Control type={type}
//         {...register(name)}
//         onBlur={onBlurHandler}
//         isInvalid={error ? true : false}
//         isValid={success ? true : false}
//         disabled={disabled}
//         />
//         <Form.Control.Feedback type="invalid">{error}</Form.Control.Feedback>
//         <Form.Control.Feedback type="valid">{success}</Form.Control.Feedback>
//         {formText && <Form.Text muted>{formText}</Form.Text>}
//         {/* {success && <Form.Text muted>{success}</Form.Text>} */}
//     </Form.Group>    
//   )
// }

// export default Input



import type React from "react";
import { Form } from "react-bootstrap";
import type { FieldValues, Path, UseFormRegister } from "react-hook-form";


type InputProps<TFieldValues extends FieldValues> = {
  label: string;
  type?: string;
  name: Path<TFieldValues>;
  register: UseFormRegister<TFieldValues>;
  error?: string;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  formText?: string;
  success?: string;
};
const Input = <TFieldValues extends FieldValues>({
  label,
  type = "text",
  name,
  register,
  error,
  onBlur,
  formText,
  success,
}: InputProps<TFieldValues>) => {
  const onBlurHandler = (e: React.FocusEvent<HTMLInputElement>) => {
    if (onBlur) {
      onBlur(e);
      register(name).onBlur(e);
    } else {
      register(name).onBlur(e);
    }
  };
  return (
    <Form.Group className="mb-3">
      <Form.Label>{label}</Form.Label>
      <Form.Control
        type={type}
        placeholder={`Enter your ${label}`}
        {...register(name)}
        onBlur={onBlurHandler}
        isInvalid={error ? true : false}
        isValid={success ? true : false}
      />
      <Form.Control.Feedback type="invalid">{error}</Form.Control.Feedback>
      <Form.Control.Feedback type="valid">{success}</Form.Control.Feedback>
      {formText && <Form.Text muted>{formText}</Form.Text>}
    </Form.Group>
  );
};

export default Input